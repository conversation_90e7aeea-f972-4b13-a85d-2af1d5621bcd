import { FastifyPluginAsync } from 'fastify';
import { UserSettingsController } from '../controllers/user/user-settings.controller.js';
import { userSettingsSchemas } from '../schemas/user/user-settings.schemas.js';
import { requireAuth } from '../lib/auth.js';

const userSettingsController = new UserSettingsController();

export const userSettingsRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get user settings
  fastify.get('/settings', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['User Settings'],
      summary: 'Get user settings',
      description: 'Retrieve the current user\'s settings for attachment processing and storage configuration.',
      response: {
        200: userSettingsSchemas.GetUserSettingsResponse,
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      }
    }
  }, userSettingsController.getSettings.bind(userSettingsController));

  // Update user settings
  fastify.put('/settings', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['User Settings'],
      summary: 'Update user settings',
      description: 'Update the current user\'s settings. Pro features require an active Pro subscription.',
      body: userSettingsSchemas.UpdateUserSettingsRequest,
      response: {
        200: userSettingsSchemas.UpdateUserSettingsResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        402: { $ref: 'ErrorResponse#' }, // Payment Required for Pro features
        500: errorResponseSchema
      }
    }
  }, userSettingsController.updateSettings.bind(userSettingsController));

  // Reset user settings to defaults
  fastify.delete('/settings', {
    preHandler: [requireAuth()],
    schema: {
      tags: ['User Settings'],
      summary: 'Reset user settings',
      description: 'Reset the current user\'s settings to default values.',
      response: {
        200: userSettingsSchemas.ResetUserSettingsResponse,
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      }
    }
  }, userSettingsController.resetSettings.bind(userSettingsController));
};
