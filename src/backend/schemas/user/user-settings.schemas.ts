export const userSettingsSchemas = {
  // Response schemas
  UserSettingsResponse: {
    type: 'object' as const,
    properties: {
      id: { type: 'string' as const, nullable: true },
      maxInlineSize: { type: 'number' as const, minimum: 0.1, maximum: 10 },
      allowMediaFiles: { type: 'boolean' as const },
      storageProvider: { type: 'string' as const, enum: ['default', 's3-compatible'] },
      s3Config: {
        type: 'object' as const,
        nullable: true,
        properties: {
          region: { type: 'string' as const },
          bucket: { type: 'string' as const },
          accessKey: { type: 'string' as const },
          secretKey: { type: 'string' as const },
          endpoint: { type: 'string' as const, nullable: true }
        }
      },
      createdAt: { type: 'string' as const, format: 'date-time', nullable: true },
      updatedAt: { type: 'string' as const, format: 'date-time', nullable: true }
    }
  },

  GetUserSettingsResponse: {
    type: 'object' as const,
    properties: {
      success: { type: 'boolean' as const },
      settings: { $ref: '#/components/schemas/UserSettingsResponse' }
    },
    required: ['success', 'settings']
  },

  UpdateUserSettingsResponse: {
    type: 'object' as const,
    properties: {
      success: { type: 'boolean' as const },
      message: { type: 'string' as const },
      settings: { $ref: '#/components/schemas/UserSettingsResponse' }
    },
    required: ['success', 'message', 'settings']
  },

  ResetUserSettingsResponse: {
    type: 'object' as const,
    properties: {
      success: { type: 'boolean' as const },
      message: { type: 'string' as const }
    },
    required: ['success', 'message']
  },

  // Request schemas
  UpdateUserSettingsRequest: {
    type: 'object' as const,
    properties: {
      maxInlineSize: {
        type: 'number' as const,
        minimum: 0.1,
        maximum: 10,
        description: 'Maximum attachment size in MB (Pro: up to 10MB, Free: up to 1MB)'
      },
      allowMediaFiles: {
        type: 'boolean' as const,
        description: 'Allow processing of media files like images and videos (Pro only)'
      },
      storageProvider: {
        type: 'string' as const,
        enum: ['default', 's3-compatible'],
        description: 'Storage provider to use (s3-compatible requires Pro plan)'
      },
      s3Config: {
        type: 'object' as const,
        nullable: true,
        description: 'S3-compatible storage configuration (Pro only)',
        properties: {
          region: {
            type: 'string' as const,
            minLength: 2,
            description: 'AWS region or S3-compatible region'
          },
          bucket: {
            type: 'string' as const,
            minLength: 3,
            description: 'S3 bucket name'
          },
          accessKey: {
            type: 'string' as const,
            minLength: 10,
            description: 'S3 access key ID'
          },
          secretKey: {
            type: 'string' as const,
            minLength: 10,
            description: 'S3 secret access key'
          },
          endpoint: {
            type: 'string' as const,
            format: 'url',
            nullable: true,
            description: 'Custom S3 endpoint URL (optional, for S3-compatible services)'
          }
        },
        required: ['region', 'bucket', 'accessKey', 'secretKey']
      }
    },
    additionalProperties: false
  }
};
