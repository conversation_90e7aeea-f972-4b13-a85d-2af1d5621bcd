import { ref, computed } from 'vue'
import { useAuth } from './useAuth'

export interface UserSettings {
  id?: string | null
  maxInlineSize: number
  allowMediaFiles: boolean
  storageProvider: string
  s3Config: {
    region: string
    bucket: string
    accessKey: string
    secretKey: string
    endpoint: string
  } | null
  createdAt?: string | null
  updatedAt?: string | null
}

export interface UpdateUserSettingsData {
  maxInlineSize?: number
  allowMediaFiles?: boolean
  storageProvider?: string
  s3Config?: {
    region?: string
    bucket?: string
    accessKey?: string
    secretKey?: string
    endpoint?: string
  } | null
}

const settings = ref<UserSettings>({
  maxInlineSize: 1.0,
  allowMediaFiles: false,
  storageProvider: 'default',
  s3Config: null
})

const loading = ref(false)
const saving = ref(false)
const error = ref<string | null>(null)

export function useUserSettings() {
  const { getAuthHeaders } = useAuth()

  const loadSettings = async () => {
    loading.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/settings', {
        method: 'GET',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to load settings')
      }

      const data = await response.json()
      
      // Update settings with loaded data
      settings.value = {
        ...data.settings,
        s3Config: data.settings.s3Config || {
          region: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          endpoint: ''
        }
      }
    } catch (err: any) {
      error.value = err.message
      console.error('Failed to load user settings:', err)
    } finally {
      loading.value = false
    }
  }

  const saveSettings = async (updates: UpdateUserSettingsData) => {
    saving.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to save settings')
      }

      const data = await response.json()
      
      // Update local settings with saved data
      settings.value = {
        ...data.settings,
        s3Config: data.settings.s3Config || {
          region: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          endpoint: ''
        }
      }

      return data
    } catch (err: any) {
      error.value = err.message
      console.error('Failed to save user settings:', err)
      throw err
    } finally {
      saving.value = false
    }
  }

  const resetSettings = async () => {
    saving.value = true
    error.value = null
    
    try {
      const response = await fetch('/api/settings', {
        method: 'DELETE',
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to reset settings')
      }

      // Reset to default values
      settings.value = {
        maxInlineSize: 1.0,
        allowMediaFiles: false,
        storageProvider: 'default',
        s3Config: {
          region: '',
          bucket: '',
          accessKey: '',
          secretKey: '',
          endpoint: ''
        }
      }

      const data = await response.json()
      return data
    } catch (err: any) {
      error.value = err.message
      console.error('Failed to reset user settings:', err)
      throw err
    } finally {
      saving.value = false
    }
  }

  // Computed properties
  const hasCustomS3Config = computed(() => {
    return settings.value.storageProvider === 's3-compatible' && 
           settings.value.s3Config &&
           settings.value.s3Config.region &&
           settings.value.s3Config.bucket &&
           settings.value.s3Config.accessKey &&
           settings.value.s3Config.secretKey
  })

  return {
    // State
    settings: computed(() => settings.value),
    loading: computed(() => loading.value),
    saving: computed(() => saving.value),
    error: computed(() => error.value),
    
    // Computed
    hasCustomS3Config,
    
    // Methods
    loadSettings,
    saveSettings,
    resetSettings
  }
}
